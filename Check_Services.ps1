# === CONFIGURATION ===
$CheckBaseName = "Service"
$Type = "Service"
$Category = "System Integrity"
$Metric = "Windows service running status"
$Unit = "boolean"

# List of services to monitor
[string[]]$TargetServices = @(
    'camsvc',
    'cbdhsvc_48f037',
    'CDPSvc',
    'CDPUserSvc_48f037',
    'CertPropSvc',
    'ClickToRunSvc',
    'ClientManager',
    'CoreMessagingRegistrar',
    'cplspcon',
    'CryptSvc',
    'DcomLaunch'
)

# === HELPER: Get service status safely ===
function Get-ServiceStatus {
    param([string]$Name)
    try {
        $svc = Get-Service -Name $Name -ErrorAction Stop
        $result = @{
            Success = ($svc.Status -eq 'Running')
            Error   = $null
            Output  = @{
                Name        = $svc.Name
                DisplayName = $svc.DisplayName
                Status      = $svc.Status.ToString()
            }
        }
    }
    catch {
        $msg = $_.Exception.Message
        if ($msg -like "*cannot find any service*") {
            $result = @{
                Success = $false
                Error   = "Service not found"
                Output  = @{
                    Name        = $Name
                    DisplayName = ""
                    Status      = "NotFound"
                }
            }
        }
        elseif ($msg -like "*access*denied*") {
            $result = @{
                Success = $false
                Error   = "Access denied. Cannot query service: $Name"
                Output  = @{
                    Name        = $Name
                    DisplayName = ""
                    Status      = "Access Denied"
                }
            }
        }
        else {
            $shortMsg = if ($msg.Length -gt 500) { $msg.Substring(0,500) } else { $msg }
            $result = @{
                Success = $false
                Error   = $shortMsg
                Output  = @{
                    Name        = $Name
                    DisplayName = ""
                    Status      = "Error"
                }
            }
        }
    }
    return $result
}

# === MAIN ===
$AllResults = @()

foreach ($svcName in $TargetServices) {
    $name = "MVTSVR $CheckBaseName $svcName"
    Write-Host ("Checking service: " + $svcName) -ForegroundColor Cyan

    $result = Get-ServiceStatus -Name $svcName

    $check = @{
        Name      = $name
        Type      = $Type
        Category  = $Category
        Metric    = $Metric
        Unit      = $Unit
        Success   = $result.Success
        Timestamp = (Get-Date).ToString("s")   # no "o" format in PSv2
        Error     = $result.Error
        Output    = $result.Output
        CsvPath   = $null
    }

    $AllResults += $check
}

# === JSON SERIALIZATION (since PSv2 has no ConvertTo-Json) ===
Add-Type -AssemblyName System.Web.Extensions
$JavaScriptSerializer = New-Object System.Web.Script.Serialization.JavaScriptSerializer
$JsonPayload = $JavaScriptSerializer.Serialize($AllResults)

# Example: save to file
$OutputFile = "MonitoringResults.json"
Set-Content -Path $OutputFile -Value $JsonPayload -Encoding UTF8
Write-Output "✅ Service checks completed. Results saved to $OutputFile"
