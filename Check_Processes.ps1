# === CONFIGURATION ===
# $ApiEndpoint = "http://localhost:8888/MonitoringResults"
# $OutputFile = "MonitoringResults.json"
# $ApiKey = "V7xP5wR8tY6mN1oA4hB7cE0dF9gH2jK5nM8pQ3rT6vW7yZ1"

# === HELPER: Get CPU Usage (PowerShell 2.0 compatible) ===
function Get-CpuUsage {
    try {
        # Use WMI instead of Get-Counter for PowerShell 2.0 compatibility
        $cpu = Get-WmiObject -Class Win32_Processor | Measure-Object -Property LoadPercentage -Average
        $value = [math]::Round($cpu.Average, 2)

        $outputObj = New-Object PSObject
        $outputObj | Add-Member -MemberType NoteProperty -Name "UsagePercent" -Value $value

        $resultObj = New-Object PSObject
        $resultObj | Add-Member -MemberType NoteProperty -Name "Success" -Value $true
        $resultObj | Add-Member -MemberType NoteProperty -Name "Error" -Value $null
        $resultObj | Add-Member -MemberType NoteProperty -Name "Output" -Value $outputObj
        return $resultObj
    }
    catch {
        $resultObj = New-Object PSObject
        $resultObj | Add-Member -MemberType NoteProperty -Name "Success" -Value $false
        $resultObj | Add-Member -MemberType NoteProperty -Name "Error" -Value "Failed to get CPU usage: $($_.Exception.Message)"
        $resultObj | Add-Member -MemberType NoteProperty -Name "Output" -Value $null
        return $resultObj
    }
}

# === HELPER: Get Memory Usage (PowerShell 2.0 compatible) ===
function Get-MemoryUsage {
    try {
        $os = Get-WmiObject Win32_OperatingSystem
        $totalMB = [math]::Round($os.TotalVisibleMemorySize / 1KB, 2)
        $freeMB  = [math]::Round($os.FreePhysicalMemory / 1KB, 2)
        $usedMB  = [math]::Round($totalMB - $freeMB, 2)
        $usedPct = [math]::Round(($usedMB / $totalMB) * 100, 2)

        $outputObj = New-Object PSObject
        $outputObj | Add-Member -MemberType NoteProperty -Name "TotalMB" -Value $totalMB
        $outputObj | Add-Member -MemberType NoteProperty -Name "UsedMB" -Value $usedMB
        $outputObj | Add-Member -MemberType NoteProperty -Name "FreeMB" -Value $freeMB
        $outputObj | Add-Member -MemberType NoteProperty -Name "UsedPercent" -Value $usedPct

        $resultObj = New-Object PSObject
        $resultObj | Add-Member -MemberType NoteProperty -Name "Success" -Value $true
        $resultObj | Add-Member -MemberType NoteProperty -Name "Error" -Value $null
        $resultObj | Add-Member -MemberType NoteProperty -Name "Output" -Value $outputObj
        return $resultObj
    }
    catch {
        $resultObj = New-Object PSObject
        $resultObj | Add-Member -MemberType NoteProperty -Name "Success" -Value $false
        $resultObj | Add-Member -MemberType NoteProperty -Name "Error" -Value "Failed to get memory usage: $($_.Exception.Message)"
        $resultObj | Add-Member -MemberType NoteProperty -Name "Output" -Value $null
        return $resultObj
    }
}

# === HELPER: Get Disk Usage (per drive) (PowerShell 2.0 compatible) ===
function Get-DiskUsage {
    try {
        $drives = New-Object System.Collections.ArrayList
        $diskObjects = Get-WmiObject Win32_LogicalDisk -Filter "DriveType=3"

        foreach ($disk in $diskObjects) {
            $sizeGB = if ($disk.Size) { [math]::Round($disk.Size / 1GB, 2) } else { 0 }
            $freeGB = if ($disk.FreeSpace) { [math]::Round($disk.FreeSpace / 1GB, 2) } else { 0 }
            $usedGB = [math]::Round($sizeGB - $freeGB, 2)
            $usedPct = if ($sizeGB -gt 0) { [math]::Round(($usedGB / $sizeGB) * 100, 2) } else { 0 }

            $outputObj = New-Object PSObject
            $outputObj | Add-Member -MemberType NoteProperty -Name "Drive" -Value $disk.DeviceID
            $outputObj | Add-Member -MemberType NoteProperty -Name "SizeGB" -Value $sizeGB
            $outputObj | Add-Member -MemberType NoteProperty -Name "UsedGB" -Value $usedGB
            $outputObj | Add-Member -MemberType NoteProperty -Name "FreeGB" -Value $freeGB
            $outputObj | Add-Member -MemberType NoteProperty -Name "UsedPercent" -Value $usedPct

            $driveResult = New-Object PSObject
            $driveResult | Add-Member -MemberType NoteProperty -Name "Success" -Value $true
            $driveResult | Add-Member -MemberType NoteProperty -Name "Error" -Value $null
            $driveResult | Add-Member -MemberType NoteProperty -Name "Output" -Value $outputObj

            $drives.Add($driveResult) | Out-Null
        }
        return $drives
    }
    catch {
        $errorResult = New-Object PSObject
        $errorResult | Add-Member -MemberType NoteProperty -Name "Success" -Value $false
        $errorResult | Add-Member -MemberType NoteProperty -Name "Error" -Value "Failed to get disk usage: $($_.Exception.Message)"
        $errorResult | Add-Member -MemberType NoteProperty -Name "Output" -Value $null

        $errorArray = New-Object System.Collections.ArrayList
        $errorArray.Add($errorResult) | Out-Null
        return $errorArray
    }
}

# === HELPER: Get Uptime (PowerShell 2.0 compatible) ===
function Get-Uptime {
    try {
        $os = Get-WmiObject Win32_OperatingSystem
        $lastBoot = $os.ConvertToDateTime($os.LastBootUpTime)
        $uptimeSpan = (Get-Date) - $lastBoot
        $uptimeFormatted = "{0}d {1}h {2}m" -f $uptimeSpan.Days, $uptimeSpan.Hours, $uptimeSpan.Minutes
        $uptimeSeconds = [int]$uptimeSpan.TotalSeconds

        $outputObj = New-Object PSObject
        $outputObj | Add-Member -MemberType NoteProperty -Name "UptimeFormatted" -Value $uptimeFormatted
        $outputObj | Add-Member -MemberType NoteProperty -Name "UptimeSeconds" -Value $uptimeSeconds

        $resultObj = New-Object PSObject
        $resultObj | Add-Member -MemberType NoteProperty -Name "Success" -Value $true
        $resultObj | Add-Member -MemberType NoteProperty -Name "Error" -Value $null
        $resultObj | Add-Member -MemberType NoteProperty -Name "Output" -Value $outputObj
        return $resultObj
    }
    catch {
        $resultObj = New-Object PSObject
        $resultObj | Add-Member -MemberType NoteProperty -Name "Success" -Value $false
        $resultObj | Add-Member -MemberType NoteProperty -Name "Error" -Value "Failed to get uptime: $($_.Exception.Message)"
        $resultObj | Add-Member -MemberType NoteProperty -Name "Output" -Value $null
        return $resultObj
    }
}

# === MAIN: Collect All Metrics (PowerShell 2.0 compatible) ===
$AllResults = New-Object System.Collections.ArrayList
$Timestamp = (Get-Date).ToString("s")

# 1. CPU Usage
$cpuResult = Get-CpuUsage
$cpuCheck = New-Object PSObject
$cpuCheck | Add-Member -MemberType NoteProperty -Name "Name" -Value "MVTSVR CPU Usage"
$cpuCheck | Add-Member -MemberType NoteProperty -Name "Type" -Value "System"
$cpuCheck | Add-Member -MemberType NoteProperty -Name "Category" -Value "CPU"
$cpuCheck | Add-Member -MemberType NoteProperty -Name "Metric" -Value "Total CPU utilization"
$cpuCheck | Add-Member -MemberType NoteProperty -Name "Unit" -Value "%"
$cpuCheck | Add-Member -MemberType NoteProperty -Name "Success" -Value $cpuResult.Success
$cpuCheck | Add-Member -MemberType NoteProperty -Name "Timestamp" -Value $Timestamp
$cpuCheck | Add-Member -MemberType NoteProperty -Name "Error" -Value $cpuResult.Error
$cpuCheck | Add-Member -MemberType NoteProperty -Name "Output" -Value $cpuResult.Output
$cpuCheck | Add-Member -MemberType NoteProperty -Name "CsvPath" -Value $null
$AllResults.Add($cpuCheck) | Out-Null

# 2. Memory Usage
$memResult = Get-MemoryUsage
$memCheck = New-Object PSObject
$memCheck | Add-Member -MemberType NoteProperty -Name "Name" -Value "MVTSVR Memory Usage"
$memCheck | Add-Member -MemberType NoteProperty -Name "Type" -Value "System"
$memCheck | Add-Member -MemberType NoteProperty -Name "Category" -Value "Memory"
$memCheck | Add-Member -MemberType NoteProperty -Name "Metric" -Value "Physical memory usage"
$memCheck | Add-Member -MemberType NoteProperty -Name "Unit" -Value "MB"
$memCheck | Add-Member -MemberType NoteProperty -Name "Success" -Value $memResult.Success
$memCheck | Add-Member -MemberType NoteProperty -Name "Timestamp" -Value $Timestamp
$memCheck | Add-Member -MemberType NoteProperty -Name "Error" -Value $memResult.Error
$memCheck | Add-Member -MemberType NoteProperty -Name "Output" -Value $memResult.Output
$memCheck | Add-Member -MemberType NoteProperty -Name "CsvPath" -Value $null
$AllResults.Add($memCheck) | Out-Null

# 3. Disk Usage
$diskResults = Get-DiskUsage
foreach ($drive in $diskResults) {
    $driveId = $drive.Output.Drive.Trim(':')
    $diskCheck = New-Object PSObject
    $diskCheck | Add-Member -MemberType NoteProperty -Name "Name" -Value "MVTSVR Disk Usage $driveId"
    $diskCheck | Add-Member -MemberType NoteProperty -Name "Type" -Value "System"
    $diskCheck | Add-Member -MemberType NoteProperty -Name "Category" -Value "Disk"
    $diskCheck | Add-Member -MemberType NoteProperty -Name "Metric" -Value "Logical disk usage"
    $diskCheck | Add-Member -MemberType NoteProperty -Name "Unit" -Value "%"
    $diskCheck | Add-Member -MemberType NoteProperty -Name "Success" -Value $drive.Success
    $diskCheck | Add-Member -MemberType NoteProperty -Name "Timestamp" -Value $Timestamp
    $diskCheck | Add-Member -MemberType NoteProperty -Name "Error" -Value $drive.Error
    $diskCheck | Add-Member -MemberType NoteProperty -Name "Output" -Value $drive.Output
    $diskCheck | Add-Member -MemberType NoteProperty -Name "CsvPath" -Value $null
    $AllResults.Add($diskCheck) | Out-Null
}

# 4. Uptime
$uptimeResult = Get-Uptime
$uptimeCheck = New-Object PSObject
$uptimeCheck | Add-Member -MemberType NoteProperty -Name "Name" -Value "MVTSVR System Uptime"
$uptimeCheck | Add-Member -MemberType NoteProperty -Name "Type" -Value "System"
$uptimeCheck | Add-Member -MemberType NoteProperty -Name "Category" -Value "System"
$uptimeCheck | Add-Member -MemberType NoteProperty -Name "Metric" -Value "Time since last reboot"
$uptimeCheck | Add-Member -MemberType NoteProperty -Name "Unit" -Value "time"
$uptimeCheck | Add-Member -MemberType NoteProperty -Name "Success" -Value $uptimeResult.Success
$uptimeCheck | Add-Member -MemberType NoteProperty -Name "Timestamp" -Value $Timestamp
$uptimeCheck | Add-Member -MemberType NoteProperty -Name "Error" -Value $uptimeResult.Error
$uptimeCheck | Add-Member -MemberType NoteProperty -Name "Output" -Value $uptimeResult.Output
$uptimeCheck | Add-Member -MemberType NoteProperty -Name "CsvPath" -Value $null
$AllResults.Add($uptimeCheck) | Out-Null

# === JSON SERIALIZATION (no ConvertTo-Json in PSv2) ===
Add-Type -AssemblyName System.Web.Extensions
$JavaScriptSerializer = New-Object System.Web.Script.Serialization.JavaScriptSerializer
$JsonPayload = $JavaScriptSerializer.Serialize($AllResults)

# Save to file
$OutputFile = "MonitoringResults.json"
Set-Content -Path $OutputFile -Value $JsonPayload -Encoding UTF8
Write-Output "✅ System metrics collected. Results saved to $OutputFile"

# === OPTIONAL: POST TO API (PSv2 compatible) ===
# if ($ApiEndpoint) {
#     try {
#         $wc = New-Object System.Net.WebClient
#         $wc.Headers.Add("Content-Type", "application/json")
#         $wc.Headers.Add("X-API-Key", $ApiKey)
#         $response = $wc.UploadString($ApiEndpoint, "POST", $JsonPayload)
#         Write-Output "✅ Successfully posted results to $ApiEndpoint"
#     }
#     catch {
#         Write-Error ("❌ Failed to send data to " + $ApiEndpoint + ". Error: " + $_.Exception.Message)
#     }
# }
