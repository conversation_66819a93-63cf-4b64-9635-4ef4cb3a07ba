# === CONFIGURATION ===
# $ApiEndpoint = "http://localhost:8888/MonitoringResults"
# $OutputFile = "MonitoringResults.json"
# $ApiKey = "V7xP5wR8tY6mN1oA4hB7cE0dF9gH2jK5nM8pQ3rT6vW7yZ1"

# === HELPER: Get CPU Usage ===
function Get-CpuUsage {
    try {
        $value = (Get-Counter '\Processor(_Total)\% Processor Time').CounterSamples.CookedValue
        $value = [math]::Round($value, 2)
        return @{
            Success = $true
            Error   = $null
            Output  = @{ UsagePercent = $value }
        }
    }
    catch {
        return @{
            Success = $false
            Error   = "Failed to get CPU usage: $($_.Exception.Message)"
            Output  = $null
        }
    }
}

# === HELPER: Get Memory Usage ===
function Get-MemoryUsage {
    try {
        $os = Get-WmiObject Win32_OperatingSystem
        $totalMB = [math]::Round($os.TotalVisibleMemorySize / 1KB, 2)
        $freeMB  = [math]::Round($os.FreePhysicalMemory / 1KB, 2)
        $usedMB  = [math]::Round($totalMB - $freeMB, 2)
        $usedPct = [math]::Round(($usedMB / $totalMB) * 100, 2)

        return @{
            Success = $true
            Error   = $null
            Output  = @{
                TotalMB     = $totalMB
                UsedMB      = $usedMB
                FreeMB      = $freeMB
                UsedPercent = $usedPct
            }
        }
    }
    catch {
        return @{
            Success = $false
            Error   = "Failed to get memory usage: $($_.Exception.Message)"
            Output  = $null
        }
    }
}

# === HELPER: Get Disk Usage (per drive) ===
function Get-DiskUsage {
    try {
        $drives = Get-WmiObject Win32_LogicalDisk -Filter "DriveType=3" | ForEach-Object {
            $sizeGB = if ($_.Size) { [math]::Round($_.Size / 1GB, 2) } else { 0 }
            $freeGB = if ($_.FreeSpace) { [math]::Round($_.FreeSpace / 1GB, 2) } else { 0 }
            $usedGB = [math]::Round($sizeGB - $freeGB, 2)
            $usedPct = if ($sizeGB -gt 0) { [math]::Round(($usedGB / $sizeGB) * 100, 2) } else { 0 }

            @{
                Success = $true
                Error   = $null
                Output  = @{
                    Drive       = $_.DeviceID
                    SizeGB      = $sizeGB
                    UsedGB      = $usedGB
                    FreeGB      = $freeGB
                    UsedPercent = $usedPct
                }
            }
        }
        return $drives
    }
    catch {
        return @(@{
            Success = $false
            Error   = "Failed to get disk usage: $($_.Exception.Message)"
            Output  = $null
        })
    }
}

# === HELPER: Get Uptime ===
function Get-Uptime {
    try {
        $os = Get-WmiObject Win32_OperatingSystem
        $lastBoot = $os.ConvertToDateTime($os.LastBootUpTime)
        $uptimeSpan = (Get-Date) - $lastBoot
        $uptimeFormatted = "{0}d {1}h {2}m" -f $uptimeSpan.Days, $uptimeSpan.Hours, $uptimeSpan.Minutes
        $uptimeSeconds = [int]$uptimeSpan.TotalSeconds

        return @{
            Success = $true
            Error   = $null
            Output  = @{
                UptimeFormatted = $uptimeFormatted
                UptimeSeconds   = $uptimeSeconds
            }
        }
    }
    catch {
        return @{
            Success = $false
            Error   = "Failed to get uptime: $($_.Exception.Message)"
            Output  = $null
        }
    }
}

# === MAIN: Collect All Metrics ===
$AllResults = @()
$Timestamp = (Get-Date).ToString("s")

# 1. CPU Usage
$cpuResult = Get-CpuUsage
$AllResults += @{
    Name      = "MVTSVR CPU Usage"
    Type      = "System"
    Category  = "CPU"
    Metric    = "Total CPU utilization"
    Unit      = "%"
    Success   = $cpuResult.Success
    Timestamp = $Timestamp
    Error     = $cpuResult.Error
    Output    = $cpuResult.Output
    CsvPath   = $null
}

# 2. Memory Usage
$memResult = Get-MemoryUsage
$AllResults += @{
    Name      = "MVTSVR Memory Usage"
    Type      = "System"
    Category  = "Memory"
    Metric    = "Physical memory usage"
    Unit      = "MB"
    Success   = $memResult.Success
    Timestamp = $Timestamp
    Error     = $memResult.Error
    Output    = $memResult.Output
    CsvPath   = $null
}

# 3. Disk Usage
$diskResults = Get-DiskUsage
foreach ($drive in $diskResults) {
    $driveId = $drive.Output.Drive.Trim(':')
    $AllResults += @{
        Name      = "MVTSVR Disk Usage $driveId"
        Type      = "System"
        Category  = "Disk"
        Metric    = "Logical disk usage"
        Unit      = "%"
        Success   = $drive.Success
        Timestamp = $Timestamp
        Error     = $drive.Error
        Output    = $drive.Output
        CsvPath   = $null
    }
}

# 4. Uptime
$uptimeResult = Get-Uptime
$AllResults += @{
    Name      = "MVTSVR System Uptime"
    Type      = "System"
    Category  = "System"
    Metric    = "Time since last reboot"
    Unit      = "time"
    Success   = $uptimeResult.Success
    Timestamp = $Timestamp
    Error     = $uptimeResult.Error
    Output    = $uptimeResult.Output
    CsvPath   = $null
}

# === JSON SERIALIZATION (no ConvertTo-Json in PSv2) ===
Add-Type -AssemblyName System.Web.Extensions
$JavaScriptSerializer = New-Object System.Web.Script.Serialization.JavaScriptSerializer
$JsonPayload = $JavaScriptSerializer.Serialize($AllResults)

# Save to file
$OutputFile = "MonitoringResults.json"
Set-Content -Path $OutputFile -Value $JsonPayload -Encoding UTF8
Write-Output "✅ System metrics collected. Results saved to $OutputFile"

# === OPTIONAL: POST TO API (PSv2 compatible) ===
# if ($ApiEndpoint) {
#     try {
#         $wc = New-Object System.Net.WebClient
#         $wc.Headers.Add("Content-Type", "application/json")
#         $wc.Headers.Add("X-API-Key", $ApiKey)
#         $response = $wc.UploadString($ApiEndpoint, "POST", $JsonPayload)
#         Write-Output "✅ Successfully posted results to $ApiEndpoint"
#     }
#     catch {
#         Write-Error ("❌ Failed to send data to " + $ApiEndpoint + ". Error: " + $_.Exception.Message)
#     }
# }
