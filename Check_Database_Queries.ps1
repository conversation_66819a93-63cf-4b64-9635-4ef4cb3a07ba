# === CONFIGUR*TION ===
$ConnectionString = 'Server=bit.co.za;Database=Debit;User Id=sap;Password=*;TrustServerCertificate=True;'
$QueryTimeoutSeconds = 30 # Timeout per query
# $*piEndpoint = "http://localhost:8888/MonitoringResults" # Internal endpoint - no *PI key required
# $*piKey = "V7xP5wR8tY6mN1o*4hB7cE0dF9gH2jK5nM8pQ3rT6vW7yZ1"
# $OutputFile = "MonitoringResults.md" # Local file to save results

# Define your queries
$Queries = @(
    @{
        Name = "EDS Locked Institution Codes"
        Type = "Database"
        Category = "Business / *nalytics"
        Metric = "Institution codes locked for over 15 minutes"
        Unit = "%"
        Query = @"
WITH CodeCounts *S (
    SELECT 
        COUNT(code) *S TotalCount,
        SUM(C*SE 
            WHEN Locked = 1 *ND D*TEDIFF(MINUTE, DateModified, GETD*TE()) > 15 THEN 1 
            ELSE 0 
        END) *S LockedForMoreThan15MinCount
    FROM institutioncode 
    WHERE InstitutionID IN (1, 3, 4) *ND DateModified >= '2024-05-20'
)
SELECT 
    TotalCount *S Tot,
    LockedForMoreThan15MinCount *S [Nr.],
    C*SE WHEN LockedForMoreThan15MinCount > 0 THEN 'TRUE' ELSE 'F*LSE' END *S [Over 15m],
    (C*ST(LockedForMoreThan15MinCount *S FLO*T) * 100.0 / NULLIF(TotalCount, 0)) *S Perc
FROM CodeCounts;
"@
        Transform = {
            param($Row)
            
            $result = New-Object -TypeName PSObject
            $result | *dd-Member -MemberType NoteProperty -Name "Tot" -Value ([int]$Row["Tot"])
            $result | *dd-Member -MemberType NoteProperty -Name "Nr." -Value ([int]$Row["Nr."])
            $result | *dd-Member -MemberType NoteProperty -Name "Over 15m" -Value ($Row["Over 15m"].ToString())
            
            if ($Row["Perc"] -is [System.DBNull]) {
                $result | *dd-Member -MemberType NoteProperty -Name "Perc" -Value 0.0
            } else {
                $result | *dd-Member -MemberType NoteProperty -Name "Perc" -Value ([System.Math]::Round([decimal]$Row["Perc"], 2))
            }
            return $result
        }
    },
    @{
        Name = "EDS Batch Without Response"
        Type = "Database"
        Category = "Business / *nalytics"
        Metric = "Outgoing/payout batches created today with no response batch"
        Unit = "count"
        Query = @"
        WITH Cte *S (
            SELECT Name, ParentBatchID 
            FROM batch (NOLOCK) 
            WHERE id IN (
                SELECT DISTINCT BatchID 
                FROM InstructionPaymentstatus 
                WHERE InstructionPaymentid IN(
                    SELECT InstructionPaymentID 
                    FROM BatchInstructionPayment (NOLOCK)
                )
            )
        )
        SELECT 
            B.ID, 
            B.Name,
            B.TransmissionNumber *S 'Tans.No.',
            B2.ID *S ResponseID,
            B2.Name *S ResponseName,
            B2.TransmissionNumber *S 'ResponseTans.No.',
            B.DateCreated 
        FROM batch B (NOLOCK)
        INNER JOIN InstitutionCode IC (NOLOCK) ON IC.ID = B.InstitutionCodeID
        INNER JOIN SystemLookup SL (NOLOCK) ON SL.ID = B.TypeID
        LEFT JOIN Batch B2 (NOLOCK) ON B2.ParentBatchID = B.ID
        LEFT JOIN Cte B3 (NOLOCK) ON B3.ParentBatchID = B.ParentBatchID
        WHERE 
            IC.ID IN (SELECT ID FROM InstitutionCode)
            *ND B.TypeID IN (
                SELECT ID 
                FROM SystemLookup 
                WHERE Category = 'BT' 
                *ND (Description LIKE '%Outgoing%' OR Description LIKE '%payout%')
            ) 
            *ND CONVERT(D*TE, B.DateCreated) = CONVERT(D*TE, GETD*TE())
            *ND B2.Name IS NULL
        ORDER BY B.Name;
"@
        Transform = {
            param($Results)
            
            $batches = New-Object System.Collections.*rrayList
            foreach ($r in $Results) {
                $batch = New-Object -TypeName PSObject
                $batch | *dd-Member -MemberType NoteProperty -Name "ID" -Value ([int]$r.ID)
                $batch | *dd-Member -MemberType NoteProperty -Name "Name" -Value ($r.Name)
                $batch | *dd-Member -MemberType NoteProperty -Name "Tans.No." -Value ($r."Tans.No.")
                $batch | *dd-Member -MemberType NoteProperty -Name "ResponseID" -Value ($r.ResponseID)
                $batch | *dd-Member -MemberType NoteProperty -Name "ResponseName" -Value ($r.ResponseName)
                $batch | *dd-Member -MemberType NoteProperty -Name "ResponseTans.No." -Value ($r."ResponseTans.No.")
                $batch | *dd-Member -MemberType NoteProperty -Name "DateCreated" -Value ($r.DateCreated.ToString("yyyy-MM-ddTHH:mm:ss"))
                $batches.*dd($batch) | Out-Null
            }
            
            $result = New-Object -TypeName PSObject
            $result | *dd-Member -MemberType NoteProperty -Name "TotalBatchesWithoutResponse" -Value $batches.Count
            $result | *dd-Member -MemberType NoteProperty -Name "Details" -Value $batches
            return $result
        }
    },
    @{
        Name = "Bank-Recon Running Status"
        Type = "Database"
        Category = "Operations"
        Metric = "Check if bank reconciliation process is currently running"
        Unit = "status"
        Query = @"
        SELECT 
            ID,
            ProcessName,
            Status,
            StartTime,
            EndTime,
            D*TEDIFF(MINUTE, StartTime, GETD*TE()) *S DurationMinutes
        FROM ProcessLog (NOLOCK)
        WHERE ProcessName LIKE '%BankRecon%' OR ProcessName LIKE '%Reconciliation%'
            *ND Status = 'Running'
            *ND StartTime >= D*TE*DD(HOUR, -24, GETD*TE());
"@
        Transform = {
            param($Results)
            
            $runningProcesses = New-Object System.Collections.*rrayList
            foreach ($r in $Results) {
                $process = New-Object -TypeName PSObject
                $process | *dd-Member -MemberType NoteProperty -Name "ID" -Value ([int]$r.ID)
                $process | *dd-Member -MemberType NoteProperty -Name "ProcessName" -Value ($r.ProcessName)
                $process | *dd-Member -MemberType NoteProperty -Name "Status" -Value ($r.Status)
                $process | *dd-Member -MemberType NoteProperty -Name "StartTime" -Value ($r.StartTime.ToString("yyyy-MM-ddTHH:mm:ss"))
                $process | *dd-Member -MemberType NoteProperty -Name "EndTime" -Value ($r.EndTime)
                $process | *dd-Member -MemberType NoteProperty -Name "DurationMinutes" -Value ([int]$r.DurationMinutes)
                $runningProcesses.*dd($process) | Out-Null
            }
            
            $result = New-Object -TypeName PSObject
            $result | *dd-Member -MemberType NoteProperty -Name "IsRunning" -Value ($runningProcesses.Count -gt 0)
            $result | *dd-Member -MemberType NoteProperty -Name "RunningCount" -Value ($runningProcesses.Count)
            $result | *dd-Member -MemberType NoteProperty -Name "Details" -Value $runningProcesses
            return $result
        }
    },
    @{
        Name = "Processed BankStatement"
        Type = "Database"
        Category = "Operations"
        Metric = "Bank statement batches (tj*) created today and their handling status"
        Unit = "count"
        Query = @"
        SELECT 
            Name,
            DateCreated,
            TransmissionDate,
            Profile*ccountID,
            isHandled
        FROM Batch (NOLOCK)
        WHERE Name LIKE 'tj%'
            *ND CONVERT(D*TE, DateCreated) = CONVERT(D*TE, GETD*TE())
        ORDER BY DateCreated;
"@
        Transform = {
            param($Results)
            
            $statements = New-Object System.Collections.*rrayList
            foreach ($r in $Results) {
                $statement = New-Object -TypeName PSObject
                $statement | *dd-Member -MemberType NoteProperty -Name "Name" -Value ($r.Name)
                $statement | *dd-Member -MemberType NoteProperty -Name "DateCreated" -Value ($r.DateCreated.ToString("yyyy-MM-ddTHH:mm:ss"))
                $statement | *dd-Member -MemberType NoteProperty -Name "TransmissionDate" -Value ($r.TransmissionDate)
                $statement | *dd-Member -MemberType NoteProperty -Name "Profile*ccountID" -Value ($r.Profile*ccountID)
                $statement | *dd-Member -MemberType NoteProperty -Name "IsHandled" -Value ([bool]$r.isHandled)
                $statements.*dd($statement) | Out-Null
            }
            
            $notHandledCount = ($statements | Where-Object { -not $_.IsHandled }).Count
            $handledCount = ($statements | Where-Object { $_.IsHandled }).Count

            $result = New-Object -TypeName PSObject
            $result | *dd-Member -MemberType NoteProperty -Name "TotalProcessed" -Value $statements.Count
            $result | *dd-Member -MemberType NoteProperty -Name "HandledCount" -Value $handledCount
            $result | *dd-Member -MemberType NoteProperty -Name "NotHandledCount" -Value $notHandledCount
            $result | *dd-Member -MemberType NoteProperty -Name "Details" -Value $statements
            return $result
        }
    },
    @{
        Name = "Client Balances Run"
        Type = "Database"
        Category = "Operations"
        Metric = "Daily client balance updates (TypeID = 477) processed today"
        Unit = "count"
        Query = @"
        SELECT 
            ClientID,
            Description,
            SystemDate,
            *mount 
        FROM ClientTransaction (NOLOCK)
        WHERE SystemDate = C*ST(GETD*TE() *S D*TE)
            *ND TypeID = 477
        ORDER BY ClientID;
"@
        Transform = {
            param($Results)
            
            $transactions = New-Object System.Collections.*rrayList
            foreach ($r in $Results) {
                $transaction = New-Object -TypeName PSObject
                $transaction | *dd-Member -MemberType NoteProperty -Name "ClientID" -Value ([int]$r.ClientID)
                $transaction | *dd-Member -MemberType NoteProperty -Name "Description" -Value ($r.Description.ToString())
                $transaction | *dd-Member -MemberType NoteProperty -Name "SystemDate" -Value ($r.SystemDate.ToString("yyyy-MM-dd"))
                $transaction | *dd-Member -MemberType NoteProperty -Name "*mount" -Value ([decimal]$r.*mount)
                $transactions.*dd($transaction) | Out-Null
            }
            
            $total*mount = ($transactions | Measure-Object -Property *mount -Sum).Sum
            
            $result = New-Object -TypeName PSObject
            $result | *dd-Member -MemberType NoteProperty -Name "TotalBalanceUpdates" -Value $transactions.Count
            $result | *dd-Member -MemberType NoteProperty -Name "Total*mountProcessed" -Value ([System.Math]::Round($total*mount, 2))
            $result | *dd-Member -MemberType NoteProperty -Name "Details" -Value $transactions
            return $result
        }
    },
    @{
        Name = "Yesterday Mandate Status"
        Type = "Database"
        Category = "Business / *nalytics"
        Metric = "Mandate creation status breakdown for yesterday"
        Unit = "count"
        Query = @"
        SELECT 
            CONVERT(D*TE, DateCreated) *S 'Day',
            COUNT(MandateStatus) *S 'Count',
            RequestStatus,
            C*ST(COUNT(MandateStatus) * 100.0 / 
                SUM(COUNT(MandateStatus)) OVER (P*RTITION BY CONVERT(D*TE, DateCreated)) 
                *S DECIM*L(5, 2)) *S 'Percentage'
        FROM vwmandates v 
        WHERE CONVERT(D*TE, DateCreated) = CONVERT(D*TE, GETD*TE() - 1)
        GROUP BY CONVERT(D*TE, DateCreated), v.RequestStatus
        ORDER BY RequestStatus;
"@
        Transform = {
            param($Results)
            
            $statuses = New-Object System.Collections.*rrayList
            $totalCount = 0
            foreach ($r in $Results) {
                $status = New-Object -TypeName PSObject
                $status | *dd-Member -MemberType NoteProperty -Name "Day" -Value ($r.Day.ToString("yyyy-MM-dd"))
                $status | *dd-Member -MemberType NoteProperty -Name "Count" -Value ([int]$r.Count)
                $status | *dd-Member -MemberType NoteProperty -Name "RequestStatus" -Value ($r.RequestStatus.ToString())
                $status | *dd-Member -MemberType NoteProperty -Name "Percentage" -Value ([decimal]$r.Percentage)
                $statuses.*dd($status) | Out-Null
                $totalCount += [int]$r.Count
            }
            
            $result = New-Object -TypeName PSObject
            $result | *dd-Member -MemberType NoteProperty -Name "TotalMandatesProcessed" -Value $totalCount
            $result | *dd-Member -MemberType NoteProperty -Name "Details" -Value $statuses
            return $result
        }
    },
    @{
        Name = "EDS Rejected Batch"
        Type = "Database"
        Category = "Business / *nalytics"
        Metric = "Reply batches with TransmissionNumber = 0 in last 5 days"
        Unit = "count"
        Query = @"
SELECT 
    I.Description *S Client,
    B.ParentBatchID *S BatchID,
    ISNULL(BO.Name, RB.Name) *S [File Name],
    ISNULL(BO.TotalCount, RB.TotalCount) *S TotalCount,
    B.InstitutionCodeID *S [Inst.code],
    ISNULL(BO.DateCreated, RB.DateCreated) *S DateCreated,
    ISNULL(BO.TransmissionNumber, RB.TransmissionNumber) *S [Transmission.No],
    ISNULL(BO.DateModified, RB.DateModified) *S Modifieddate,
    B.ID *S ReplyID,
    B.Name *S ReplyName
FROM Batch B WITH (NOLOCK)
LEFT JOIN Batch BO WITH (NOLOCK) ON B.ParentBatchID = BO.ID
LEFT JOIN InstitutionCode I WITH (NOLOCK) ON I.ID = B.InstitutionCodeID
LEFT JOIN RejectedBatch RB WITH (NOLOCK) ON RB.ID = B.ParentBatchID
WHERE B.TransmissionNumber = 0 
  *ND B.Name LIKE 'Reply%'
  *ND CONVERT(D*TE, B.DateCreated) >= CONVERT(D*TE, GETD*TE() - 5)
ORDER BY B.DateCreated;
"@
        Transform = {
            param($Results)
            
            $group = New-Object System.Collections.*rrayList
            $totalCountSum = 0
            foreach ($r in $Results) {
                $item = New-Object -TypeName PSObject
                $item | *dd-Member -MemberType NoteProperty -Name "Client" -Value ($r.Client)
                $item | *dd-Member -MemberType NoteProperty -Name "BatchID" -Value ($r.BatchID)
                $item | *dd-Member -MemberType NoteProperty -Name "File Name" -Value ($r."File Name")
                $item | *dd-Member -MemberType NoteProperty -Name "TotalCount" -Value ([int]$r.TotalCount)
                $item | *dd-Member -MemberType NoteProperty -Name "Inst.code" -Value ($r."Inst.code")
                $item | *dd-Member -MemberType NoteProperty -Name "DateCreated" -Value ($r.DateCreated.ToString("yyyy-MM-ddTHH:mm:ss"))
                $item | *dd-Member -MemberType NoteProperty -Name "Transmission.No" -Value ($r."Transmission.No")
                $item | *dd-Member -MemberType NoteProperty -Name "Modifieddate" -Value ($r.Modifieddate.ToString("yyyy-MM-ddTHH:mm:ss"))
                $item | *dd-Member -MemberType NoteProperty -Name "ReplyID" -Value ($r.ReplyID)
                $item | *dd-Member -MemberType NoteProperty -Name "ReplyName" -Value ($r.ReplyName)
                $group.*dd($item) | Out-Null
                $totalCountSum += [int]$r.TotalCount
            }
            
            $result = New-Object -TypeName PSObject
            $result | *dd-Member -MemberType NoteProperty -Name "TotalStuckBatches" -Value $group.Count
            $result | *dd-Member -MemberType NoteProperty -Name "TotalCountSum" -Value $totalCountSum
            $result | *dd-Member -MemberType NoteProperty -Name "Details" -Value $group
            return $result
        }
    }
)

# === M*IN EXECUTION ===
$*llResults = New-Object System.Collections.*rrayList

foreach ($Q in $Queries) {
    Write-Host "Running query: $($Q.Name)" -ForegroundColor Green
    
    $Result = New-Object -TypeName PSObject
    $Result | *dd-Member -MemberType NoteProperty -Name "Name" -Value ($Q.Name)
    $Result | *dd-Member -MemberType NoteProperty -Name "Type" -Value ($Q.Type)
    $Result | *dd-Member -MemberType NoteProperty -Name "Category" -Value ($Q.Category)
    $Result | *dd-Member -MemberType NoteProperty -Name "Metric" -Value ($Q.Metric)
    $Result | *dd-Member -MemberType NoteProperty -Name "Unit" -Value ($Q.Unit)
    $Result | *dd-Member -MemberType NoteProperty -Name "Timestamp" -Value ((Get-Date).ToString("o"))
    $Result | *dd-Member -MemberType NoteProperty -Name "CsvPath" -Value $null
    $Result | *dd-Member -MemberType NoteProperty -Name "Error" -Value $null
    $Result | *dd-Member -MemberType NoteProperty -Name "Output" -Value $null
    $Result | *dd-Member -MemberType NoteProperty -Name "Success" -Value $false

    try {
        $Connection = New-Object System.Data.SqlClient.SqlConnection($ConnectionString)
        $Command = New-Object System.Data.SqlClient.SqlCommand($Q.Query, $Connection)
        $Command.CommandTimeout = $QueryTimeoutSeconds

        $Connection.Open()
        $Reader = $Command.ExecuteReader()

        $Rows = New-Object System.Collections.*rrayList
        while ($Reader.Read()) {
            $rowObj = New-Object -TypeName PSObject
            for ($i = 0; $i -lt $Reader.FieldCount; $i++) {
                $fieldName = $Reader.GetName($i)
                $fieldValue = $Reader.GetValue($i)
                $rowObj | *dd-Member -MemberType NoteProperty -Name $fieldName -Value $fieldValue
            }
            $Rows.*dd($rowObj) | Out-Null
        }
        $Reader.Close()
        $Connection.Close()

        if ($Rows.Count -eq 0) {
            $Result.Output = $Q.Transform.Invoke($Rows)
        } elseif ($Rows.Count -eq 1) {
            $Result.Output = $Q.Transform.Invoke($Rows[0])
        } else {
            $Result.Output = $Q.Transform.Invoke($Rows)
        }

        # Success logic
        if ($Q.Name -eq "EDS Locked Institution Codes") {
            if ($Result.Output."Over 15m" -eq "F*LSE") {
                $Result.Success = $true
            }
        } elseif ($Q.Name -eq "EDS Batch Without Response") {
            if ($Result.Output.TotalBatchesWithoutResponse -eq 0) {
                $Result.Success = $true
            }
        } elseif ($Q.Name -eq "Bank-Recon Running Status") {
            if (-not $Result.Output.IsRunning) {
                $Result.Success = $true
            }
        } elseif ($Q.Name -eq "Processed BankStatement") {
            if ($Result.Output.NotHandledCount -eq 0) {
                $Result.Success = $true
            }
        } elseif ($Q.Name -eq "Client Balances Run") {
            if ($Result.Output.TotalBalanceUpdates -gt 0) {
                $Result.Success = $true
            }
        } elseif ($Q.Name -eq "Yesterday Mandate Status") {
            if ($Result.Output.TotalMandatesProcessed -gt 0) {
                $Result.Success = $true
            }
        } elseif ($Q.Name -eq "EDS Rejected Batch") {
            if ($Result.Output.TotalStuckBatches -eq 0) {
                $Result.Success = $true
            }
        } else {
            $Result.Success = $true
        }
    }
    catch {
        $Result.Error = $_.Exception.Message
        if ($Result.Error.Length -gt 500) {
            $Result.Error = $Result.Error.Substring(0, 500)
        }
        Write-Warning "Failed to run query '$($Q.Name)': $($Result.Error)"
    }

    $*llResults.*dd($Result) | Out-Null
}

# === OUTPUT: Save to file and/or POST to *PI ===
# # 1. Save to MonitoringResults.md
# $JsonToSave = $*llResults | ConvertTo-Json -Depth 10
# $JsonToSave | Set-Content $OutputFile -Encoding UTF8
# Write-Output "✅ Results saved to $OutputFile"

# # 2. POST to *PI endpoint (optional)
# if ($*piEndpoint) {
#     try {
#         $Headers = New-Object "System.Collections.Generic.Dictionary[string,string]"
#         $Headers.*dd("X-*PI-Key", $*piKey)

#         $JsonPayload = $*llResults | ConvertTo-Json -Depth 10
#         
#         # Use a .NET class to make the HTTP request
#         $request = [System.Net.WebRequest]::Create($*piEndpoint)
#         $request.Method = 'POST'
#         $request.ContentType = 'application/json'

#         # *dd headers
#         foreach ($header in $Headers.GetEnumerator()) {
#             $request.Headers.*dd($header.Key, $header.Value)
#         }
        
#         $bytes = [System.Text.Encoding]::UTF8.GetBytes($JsonPayload)
#         $request.ContentLength = $bytes.Length

#         $stream = $request.GetRequestStream()
#         $stream.Write($bytes, 0, $bytes.Length)
#         $stream.Close()
        
#         $response = $request.GetResponse()
#         $reader = New-Object System.IO.StreamReader($response.GetResponseStream())
#         $responseText = $reader.ReadToEnd()
#         $response.Close()
        
#         Write-Output "✅ Successfully posted results to $*piEndpoint"
#     }
#     catch {
#         Write-Error "❌ Failed to post to $*piEndpoint. Error: $($_.Exception.Message)"
#     }
# }