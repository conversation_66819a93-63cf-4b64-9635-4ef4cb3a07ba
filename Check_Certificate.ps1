# === CONFIGURATION ===
$CheckBaseName = "MVTSVR Certificate"
$Type = "Certificate"
$Category = "Security"
$Metric = 'Certificate expiry status'
$Unit = "days"
$ExpiringThresholdDays = 30 # Warn if expiry is within this many days
# $ApiEndpoint = "http://localhost:8888/MonitoringResults" # Optional: POST results here
# $ApiKey = "V7xP5wR8tY6mN1oA4hB7cE0dF9gH2jK5nM8pQ3rT6vW7yZ1"

# Set up output path
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$OutputDir = Join-Path (Split-Path -Parent $ScriptDir) "Output"
if (-not (Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
}
$OutputFile = Join-Path $OutputDir "MonitoringResults.md" # Save final JSON array

# === HELPER: Sanitize certificate subject for use in Name ===
function Get-SafeCertName {
    param([string]$Subject)
    # Extract CN if possible
    $cn = $null
    if ($Subject -match 'CN=([^,]+)') {
        $cn = $matches[1]
    } else {
        $cnParts = $Subject -split '='
        $cn = $cnParts[-1]
    }
    
    $safeName = $cn -replace '[^\w\-]', '_'
    if ($safeName.Length -gt 50) {
        return $safeName.Substring(0, 50)
    }
    return $safeName
}

# === MAIN: Check Certificates ===
$AllResults = New-Object System.Collections.ArrayList
$now = Get-Date

# Define stores to check
$storeLocations = [System.Security.Cryptography.X509Certificates.StoreLocation]::LocalMachine
# $storeLocations += [System.Security.Cryptography.X509Certificates.StoreLocation]::CurrentUser # Uncomment if needed
$storeNames = "My"

# Process single store location and name (PowerShell 2.0 compatible)
$location = $storeLocations
$storeName = $storeNames
$store = $null
try {
    Write-Host "Checking certificates in $location\$storeName store..."
    $store = New-Object System.Security.Cryptography.X509Certificates.X509Store $storeName, $location
    $store.Open([System.Security.Cryptography.X509Certificates.OpenFlags]::ReadOnly)

    foreach ($cert in $store.Certificates) {
        if ($cert -eq $null) { continue }
        if ([string]::IsNullOrEmpty($cert.Subject)) { continue }
        if ($cert.NotAfter -eq $null) { continue }

        try {
            $timeSpan = $cert.NotAfter - $now
            $daysRemaining = $timeSpan.TotalDays
            $status = ""

            if ($daysRemaining -lt 0) {
                $status = "Expired"
            } elseif ($daysRemaining -le $ExpiringThresholdDays) {
                $status = "Expiring"
            } else {
                $status = "Valid"
            }

            $safeName = Get-SafeCertName -Subject $cert.Subject
            $storePath = "$location\$storeName"

            $success = $false
            if ($status -eq "Valid") {
                $success = $true
            }

            $certError = $null
            if (-not $success) {
                if ($daysRemaining -lt 0) {
                    $certError = "Certificate expired on $($cert.NotAfter.ToString('yyyy-MM-dd'))"
                } else {
                    $certError = "Certificate expires in $([System.Math]::Floor($daysRemaining)) days"
                }
            }

            $displayName = ""
            if ($cert.Subject -match 'CN=([^,]+)') {
                $displayName = ($matches[1]).Trim('*. ')
            } else {
                $displayName = $cert.Subject
            }

            # Format certificate details for output
            $certOutput = New-Object PSObject
            $certOutput | Add-Member -MemberType NoteProperty -Name "Path" -Value $storePath
            $certOutput | Add-Member -MemberType NoteProperty -Name "Subject" -Value $cert.Subject
            $certOutput | Add-Member -MemberType NoteProperty -Name "DaysRemaining" -Value ([System.Math]::Round($daysRemaining, 1))
            $certOutput | Add-Member -MemberType NoteProperty -Name "Status" -Value $status
            $certOutput | Add-Member -MemberType NoteProperty -Name "NotAfter" -Value ($cert.NotAfter.ToString("yyyy-MM-ddTHH:mm:ss"))

            $result = New-Object PSObject
            $result | Add-Member -MemberType NoteProperty -Name "Name" -Value "$($CheckBaseName)_$($displayName -replace '[^\w\-]', '_')"
            $result | Add-Member -MemberType NoteProperty -Name "Type" -Value $Type
            $result | Add-Member -MemberType NoteProperty -Name "Category" -Value $Category
            $result | Add-Member -MemberType NoteProperty -Name "Metric" -Value $Metric
            $result | Add-Member -MemberType NoteProperty -Name "Unit" -Value $Unit
            $result | Add-Member -MemberType NoteProperty -Name "Success" -Value $success
            $result | Add-Member -MemberType NoteProperty -Name "Timestamp" -Value ($now.ToString("o"))
            $result | Add-Member -MemberType NoteProperty -Name "Error" -Value $certError
            $result | Add-Member -MemberType NoteProperty -Name "Output" -Value $certOutput
            $result | Add-Member -MemberType NoteProperty -Name "CsvPath" -Value $null

            $AllResults.Add($result) | Out-Null
            Write-Host "Processed certificate: $($cert.Subject)"
        }
        catch {
            Write-Warning "Failed to process certificate: $($cert.Subject). Error: $($_.Exception.Message)"
            continue
        }
    }
}
catch {
    $storePath = "$location\$storeName"
    $result = New-Object PSObject
    $result | Add-Member -MemberType NoteProperty -Name "Name" -Value "${CheckBaseName}_ReadError_${location}"
    $result | Add-Member -MemberType NoteProperty -Name "Type" -Value $Type
    $result | Add-Member -MemberType NoteProperty -Name "Category" -Value $Category
    $result | Add-Member -MemberType NoteProperty -Name "Metric" -Value $Metric
    $result | Add-Member -MemberType NoteProperty -Name "Unit" -Value $Unit
    $result | Add-Member -MemberType NoteProperty -Name "Success" -Value $false
    $result | Add-Member -MemberType NoteProperty -Name "Timestamp" -Value ($now.ToString("o"))
    $result | Add-Member -MemberType NoteProperty -Name "Error" -Value ("Failed to read store: $($_.Exception.Message)")
    $outputDetail = New-Object PSObject
    $outputDetail | Add-Member -MemberType NoteProperty -Name "Store" -Value $storePath
    $errorMsg = $_.Exception.Message
    if ($errorMsg.Length -gt 500) {
        $errorMsg = $errorMsg.Substring(0, 500)
    }
    $outputDetail | Add-Member -MemberType NoteProperty -Name "ErrorDetail" -Value $errorMsg
    $result | Add-Member -MemberType NoteProperty -Name "Output" -Value $outputDetail
    $result | Add-Member -MemberType NoteProperty -Name "CsvPath" -Value $null

    $AllResults.Add($result) | Out-Null
    Write-Warning "Failed to access certificate store $storePath: $($_.Exception.Message)"
}
finally {
    if ($store -ne $null) {
        $store.Close()
    }
}

# --- UNCOMMENT AND MODIFY TO ENABLE JSON OUTPUT AND API POSTING ---
# === SAVE TO FILE ===
# try {
#     $OutputFile = $OutputFile.Replace(".md", ".json")
#     
#     if (-not (Test-Path $OutputFile)) {
#         @() | ConvertTo-Json | Set-Content -Path $OutputFile -Encoding UTF8 -Force
#     }
#     
#     $existingContent = Get-Content -Path $OutputFile -Raw
#     $existingResults = New-Object System.Collections.ArrayList
#     if ($existingContent) {
#         $temp = $existingContent | ConvertFrom-Json
#         foreach ($item in $temp) { $existingResults.Add($item) | Out-Null }
#     }
# 
#     $otherResults = New-Object System.Collections.ArrayList
#     foreach ($r in $existingResults) {
#         if ($r.Type -ne $Type) { $otherResults.Add($r) | Out-Null }
#     }
#     
#     $finalResults = New-Object System.Collections.ArrayList
#     foreach ($r in $otherResults) { $finalResults.Add($r) | Out-Null }
#     foreach ($r in $AllResults) { $finalResults.Add($r) | Out-Null }
#     
#     $JsonPayload = $finalResults | ConvertTo-Json -Depth 10
#     $JsonPayload | Set-Content -Path $OutputFile -Encoding UTF8 -Force
#     Write-Host "Certificate checks completed. Results saved to $OutputFile"
# 
#     $backupFile = $OutputFile.Replace(".json", "_backup.json")
#     $JsonPayload | Set-Content -Path $backupFile -Encoding UTF8 -Force
# }
# catch {
#     Write-Error "Failed to save results to $OutputFile. Error: $($_.Exception.Message)"
#     
#     $currentDir = Split-Path -Parent $MyInvocation.MyCommand.Path
#     $fallbackPath = Join-Path $currentDir "MonitoringResults.json"
#     try {
#         $fallbackDir = Split-Path -Parent $fallbackPath
#         if (-not (Test-Path $fallbackDir)) {
#             New-Item -ItemType Directory -Path $fallbackDir -Force | Out-Null
#         }
# 
#         $existingResults = New-Object System.Collections.ArrayList
#         if (Test-Path $fallbackPath) {
#             $existingContent = Get-Content -Path $fallbackPath -Raw
#             if ($existingContent) {
#                 $temp = $existingContent | ConvertFrom-Json
#                 foreach ($item in $temp) { $existingResults.Add($item) | Out-Null }
#             }
#         }
#         
#         $otherResults = New-Object System.Collections.ArrayList
#         foreach ($r in $existingResults) {
#             if ($r.Type -ne $Type) { $otherResults.Add($r) | Out-Null }
#         }
#         
#         $finalResults = New-Object System.Collections.ArrayList
#         foreach ($r in $otherResults) { $finalResults.Add($r) | Out-Null }
#         foreach ($r in $AllResults) { $finalResults.Add($r) | Out-Null }
#         
#         $JsonPayload = $finalResults | ConvertTo-Json -Depth 10
#         $JsonPayload | Set-Content -Path $fallbackPath -Encoding UTF8 -Force
#         Write-Host "Results saved to fallback location: $fallbackPath"
#     }
#     catch {
#         Write-Error "Failed to save to fallback location: $($_.Exception.Message)"
#     }
# }

# === POST TO API (Optional) ===
# if ($ApiEndpoint) {
#     try {
#         $Headers = @{"X-API-Key" = $ApiKey}
#         
#         # Use a .NET class to make the HTTP request
#         $request = [System.Net.WebRequest]::Create($ApiEndpoint)
#         $request.Method = 'POST'
#         $request.ContentType = 'application/json'
# 
#         foreach ($key in $Headers.Keys) {
#             $request.Headers.Add($key, $Headers.$key)
#         }
#         
#         $JsonPayload = $AllResults | ConvertTo-Json -Depth 10
#         $bytes = [System.Text.Encoding]::UTF8.GetBytes($JsonPayload)
#         $request.ContentLength = $bytes.Length
# 
#         $stream = $request.GetRequestStream()
#         $stream.Write($bytes, 0, $bytes.Length)
#         $stream.Close()
#         
#         $response = $request.GetResponse()
#         $reader = New-Object System.IO.StreamReader($response.GetResponseStream())
#         $responseText = $reader.ReadToEnd()
#         $response.Close()
#         
#         Write-Host "Results posted to: $ApiEndpoint"
#     }
#     catch {
#         Write-Error "Failed to send data to $ApiEndpoint. Error: $($_.Exception.Message)"
#     }
# }