# === CONFIGURATION ===
$CheckName = "MVTSVR  Logs Application"
$Type = "Logs"
$Category = "Logs"
$Metric = "Application errors in the last 72 hours"
$Unit = "count"
$HoursBack = 72  # Look back window
# $ApiEndpoint = "http://localhost:8888/MonitoringResults" 
# $ApiKey = "V7xP5wR8tY6mN1oA4hB7cE0dF9gH2jK5nM8pQ3rT6vW7yZ1"
# $OutputFile = "MonitoringResults.md"

# === HELPER: Get Application Log Errors (PowerShell 2.0 safe) ===
function Get-ApplicationErrors {
    param([int]$HoursBack)

    try {
        $StartTime = (Get-Date).AddHours(-$HoursBack)

        # PowerShell 2.0: use Get-EventLog instead of Get-WinEvent
        $Events = Get-EventLog -LogName Application -EntryType Error |
                  Where-Object { $_.TimeGenerated -ge $StartTime }

        $ErrorList = @()
        foreach ($ev in $Events) {
            $summary = $ev.Message.Split("`n")[0].Trim()
            if ($summary.Length -gt 200) {
                $summary = $summary.Substring(0,200)
            }
            $line = "{0:u} | ID:{1} | {2} | {3}" -f $ev.TimeGenerated, $ev.EventID, $ev.Source, $summary
            $ErrorList += $line
        }

        $outputObj = New-Object PSObject
        $outputObj | Add-Member -MemberType NoteProperty -Name "TotalErrors" -Value $ErrorList.Count
        $outputObj | Add-Member -MemberType NoteProperty -Name "RecentErrors" -Value $ErrorList

        $resultObj = New-Object PSObject
        $resultObj | Add-Member -MemberType NoteProperty -Name "Success" -Value $true
        $resultObj | Add-Member -MemberType NoteProperty -Name "Error" -Value $null
        $resultObj | Add-Member -MemberType NoteProperty -Name "Output" -Value $outputObj
        return $resultObj
    }
    catch {
        $msg = $_.Exception.Message
        $outputObj = New-Object PSObject
        $outputObj | Add-Member -MemberType NoteProperty -Name "TotalErrors" -Value 0
        $outputObj | Add-Member -MemberType NoteProperty -Name "RecentErrors" -Value @()

        $resultObj = New-Object PSObject
        $resultObj | Add-Member -MemberType NoteProperty -Name "Success" -Value $false
        $resultObj | Add-Member -MemberType NoteProperty -Name "Error" -Value $msg
        $resultObj | Add-Member -MemberType NoteProperty -Name "Output" -Value $outputObj
        return $resultObj
    }
}

# === RUN CHECK ===
$result = Get-ApplicationErrors -HoursBack $HoursBack

$successFlag = $false
if ($result.Success -and ($result.Output.TotalErrors -eq 0)) {
    $successFlag = $true
}

$MonitoringResult = New-Object PSObject
$MonitoringResult | Add-Member -MemberType NoteProperty -Name "Name" -Value $CheckName
$MonitoringResult | Add-Member -MemberType NoteProperty -Name "Type" -Value $Type
$MonitoringResult | Add-Member -MemberType NoteProperty -Name "Category" -Value $Category
$MonitoringResult | Add-Member -MemberType NoteProperty -Name "Metric" -Value $Metric
$MonitoringResult | Add-Member -MemberType NoteProperty -Name "Unit" -Value $Unit
$MonitoringResult | Add-Member -MemberType NoteProperty -Name "Success" -Value $successFlag
$MonitoringResult | Add-Member -MemberType NoteProperty -Name "Timestamp" -Value ((Get-Date).ToString("o"))
$MonitoringResult | Add-Member -MemberType NoteProperty -Name "Error" -Value $result.Error
$MonitoringResult | Add-Member -MemberType NoteProperty -Name "Output" -Value $result.Output
$MonitoringResult | Add-Member -MemberType NoteProperty -Name "CsvPath" -Value $null

# === JSON Conversion for PowerShell 2.0 ===
# (since ConvertTo-Json does not exist in PS2, use .NET)
# $jsonSerializer = New-Object System.Web.Script.Serialization.JavaScriptSerializer
# $JsonPayload = $jsonSerializer.Serialize($MonitoringResult)
# Set-Content -Path $OutputFile -Value $JsonPayload -Encoding UTF8
