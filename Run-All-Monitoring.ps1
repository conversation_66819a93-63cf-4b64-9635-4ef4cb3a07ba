# ==========================================
# MAIN ORCHESTRATOR: Run All Monitoring Scripts
# ==========================================

# === CONFIGURATION ===
$ScriptPaths = ".\Check_Events_Errors.ps1", ".\Check_Certificate.ps1", ".\Check_System_Metrics.ps1"

$FinalOutputFile = "AggregatedMonitoringResults.json"
$LogFilePath = "OrchestratorExecution.log"
$ApiEndpoint = "http://localhost:8888/MonitoringResults"
$ApiKey = "V7xP5wR8tY6mN1oA4hB7cE0dF9gH2jK5nM8pQ3rT6vW7yZ1"
$ServerSource = "*******" # Default value, will be updated later

# Initialize
$AllCollectedResults = New-Object System.Collections.ArrayList
$ExecutionLog = New-Object System.Collections.ArrayList

# === HELPER: Write to Log ===
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogEntry = "[$Timestamp] [$Level] $Message"
    Add-Content -Path $LogFilePath -Value $LogEntry -Encoding UTF8
    switch ($Level) {
        "ERROR"   { Write-Host $LogEntry -ForegroundColor Red }
        "WARNING" { Write-Host $LogEntry -ForegroundColor Yellow }
        "INFO"    { Write-Host $LogEntry -ForegroundColor Gray }
        "SUCCESS" { Write-Host $LogEntry -ForegroundColor Green }
    }
}

# Custom function to serialize an object to JSON using .NET
function ConvertTo-Json20 {
    param(
        [Parameter(Mandatory=$true)]
        [object]$InputObject,
        [int]$Depth = 10
    )
    
    # Load the required .NET assembly directly.
    # This will not cause an error if the assembly is already loaded.
    try {
        Add-Type -AssemblyName System.Web.Extensions
    } catch {
        Write-Error "Failed to load System.Web.Extensions.dll. This is required for JSON support."
        return $null
    }

    # Use JavaScriptSerializer to perform the conversion
    $serializer = New-Object System.Web.Script.Serialization.JavaScriptSerializer
    # Set the recursion limit
    $serializer.RecursionLimit = $Depth
    
    # Serialize the input object and return the JSON string
    try {
        return $serializer.Serialize($InputObject)
    } catch {
        Write-Error "JSON serialization failed: $($_.Exception.Message)"
        return $null
    }
}

# === CLEAN UP OLD FILES ===
if (Test-Path $FinalOutputFile) { Remove-Item $FinalOutputFile -Force }

# Get public IP address for ServerSource using .NET
try {
    Write-Log "Attempting to get public IP address..."
    $webClient = New-Object System.Net.WebClient
    $ServerSource = $webClient.DownloadString("http://ipinfo.io/ip").Trim()
    $webClient.Dispose()
    Write-Log "Using ServerSource: $ServerSource" "INFO"
} catch {
    Write-Log "Failed to get public IP address, using localhost" "WARNING"
    $ServerSource = "127.0.0.1"
}

Write-Log "Orchestrator started. Will execute $($ScriptPaths.Count) monitoring scripts."

# === EXECUTE EACH SCRIPT ===
foreach ($ScriptPath in $ScriptPaths) {
    $ScriptName = Split-Path $ScriptPath -Leaf
    $ScriptBaseName = (Get-Item $ScriptPath).BaseName
    Write-Log "Running script: $ScriptName"

    # Clear previous result variables
    Remove-Variable -Name "MonitoringResult" -ErrorAction SilentlyContinue
    Remove-Variable -Name "AllResults" -ErrorAction SilentlyContinue

    try {
        # Dot-source the script to capture its variables
        . $ScriptPath

        # Determine result type
        $ResultsToAdd = $null
        $SourceLabel = "Unknown"

        if (Get-Variable -Name "MonitoringResult" -ErrorAction SilentlyContinue) {
            $ResultsToAdd = $MonitoringResult # Single result
            if ($ResultsToAdd -isnot [array]) {
                $ResultsToAdd = @($ResultsToAdd) # Wrap in array if not already
            }
            $SourceLabel = "SingleResult"
        }
        elseif (Get-Variable -Name "AllResults" -ErrorAction SilentlyContinue) {
            $ResultsToAdd = (Get-Variable -Name "AllResults").Value
            $SourceLabel = "MultiResult"
        }
        else {
            Write-Log "No result variable found in $ScriptName" "WARNING"
        }

        if ($ResultsToAdd) {
            # Add source script name and server source to each result for traceability
            foreach ($r in $ResultsToAdd) {
                if (-not ($r | Get-Member -Name "SourceScript")) {
                    $r | Add-Member -MemberType NoteProperty -Name "SourceScript" -Value $ScriptBaseName -Force
                }
                if (-not ($r | Get-Member -Name "ServerSource")) {
                    $r | Add-Member -MemberType NoteProperty -Name "ServerSource" -Value $ServerSource -Force
                }
            }
            $AllCollectedResults.AddRange($ResultsToAdd)
            Write-Log "✅ Collected $($ResultsToAdd.Count) results from $ScriptName [$SourceLabel]" "SUCCESS"
        }
    }
    catch {
        $ErrorMessage = $_.Exception.Message
        if ($ErrorMessage.Length -gt 500) { $ErrorMessage = $ErrorMessage.Substring(0, 500) }
        Write-Log "❌ Failed to execute $ScriptName: $ErrorMessage" "ERROR"

        # Record failure as a synthetic monitoring result
        $syntheticResult = New-Object PSObject
        $syntheticResult | Add-Member -MemberType NoteProperty -Name "SourceScript" -Value $ScriptBaseName
        $syntheticResult | Add-Member -MemberType NoteProperty -Name "Name" -Value "Script Execution Failure"
        $syntheticResult | Add-Member -MemberType NoteProperty -Name "Type" -Value "System"
        $syntheticResult | Add-Member -MemberType NoteProperty -Name "Category" -Value "Orchestration"
        $syntheticResult | Add-Member -MemberType NoteProperty -Name "Metric" -Value "PowerShell script execution"
        $syntheticResult | Add-Member -MemberType NoteProperty -Name "Unit" -Value "none"
        $syntheticResult | Add-Member -MemberType NoteProperty -Name "Success" -Value $false
        $syntheticResult | Add-Member -MemberType NoteProperty -Name "Timestamp" -Value ((Get-Date).ToString("o"))
        $syntheticResult | Add-Member -MemberType NoteProperty -Name "Error" -Value $ErrorMessage
        $syntheticResult | Add-Member -MemberType NoteProperty -Name "Output" -Value (New-Object PSObject)
        $syntheticResult | Add-Member -MemberType NoteProperty -Name "ServerSource" -Value $ServerSource
        $AllCollectedResults.Add($syntheticResult) | Out-Null
    }
}

# === FINALIZE AGGREGATED PAYLOAD ===
# Create a clean array of results to prevent circular references during serialization
$CleanResults = New-Object System.Collections.ArrayList
foreach ($r in $AllCollectedResults) {
    $tempObject = New-Object PSObject
    foreach ($prop in $r.PSObject.Properties) {
        $tempObject | Add-Member -MemberType NoteProperty -Name $prop.Name -Value $prop.Value
    }
    $CleanResults.Add($tempObject) | Out-Null
}

# Rebuild the final payload with the clean, simplified results
$FinalPayload = New-Object PSObject
$FinalPayload | Add-Member -MemberType NoteProperty -Name "ExecutionId" -Value ([Guid]::NewGuid().ToString())
$FinalPayload | Add-Member -MemberType NoteProperty -Name "Timestamp" -Value ((Get-Date).ToString("o"))
$FinalPayload | Add-Member -MemberType NoteProperty -Name "TotalResults" -Value $CleanResults.Count
$FailedCount = ($CleanResults | Where-Object { -not $_.Success }).Count
$FinalPayload | Add-Member -MemberType NoteProperty -Name "FailedResults" -Value $FailedCount
$FinalPayload | Add-Member -MemberType NoteProperty -Name "Results" -Value $CleanResults

# Convert to JSON for file storage
$JsonPayload = ConvertTo-Json20 -InputObject $FinalPayload -Depth 20

# Save to file
$JsonPayload | Set-Content -Path $FinalOutputFile -Encoding UTF8
Write-Log "✅ Aggregated results saved to $FinalOutputFile" "SUCCESS"

# === POST TO API ONCE ===
if ($ApiEndpoint) {
    try {
        Write-Log "Attempting to post results to API endpoint..."
        $request = [System.Net.WebRequest]::Create($ApiEndpoint)
        $request.Method = 'POST'
        $request.ContentType = 'application/json'
        
        # Add API Key header
        $request.Headers.Add("X-API-Key", $ApiKey)
        
        $ApiPayload = ConvertTo-Json20 -InputObject $CleanResults -Depth 10
        $bytes = [System.Text.Encoding]::UTF8.GetBytes($ApiPayload)
        $request.ContentLength = $bytes.Length
        
        $stream = $request.GetRequestStream()
        $stream.Write($bytes, 0, $bytes.Length)
        $stream.Close()
        
        $response = $request.GetResponse()
        $response.Close()
        
        Write-Log "✅ Successfully posted aggregated results to $ApiEndpoint" "SUCCESS"
    }
    catch {
        $Status = "No Response"
        if ($_.Exception.Response) { 
            try {
                $Status = [int]$_.Exception.Response.StatusCode
            }
            catch {
                $Status = "Unknown"
            }
        }
        $ErrMsg = $_.Exception.Message
        if ($ErrMsg.Length -gt 500) { $ErrMsg = $ErrMsg.Substring(0,500) }
        Write-Log "❌ Failed to post to $ApiEndpoint. Status: $Status. Error: $ErrMsg" "ERROR"
    }
}

# === FINAL SUMMARY ===
$totalResults = $AllCollectedResults.Count
$failedResults = ($AllCollectedResults | Where-Object { -not $_.Success }).Count
Write-Log "Orchestration completed. $totalResults results collected, $failedResults failed." "SUCCESS"