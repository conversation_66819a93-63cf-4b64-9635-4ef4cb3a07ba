# === CONFIGURATION ===
$CheckBaseName = "Process"
$Type = "Process"
$Category = "Processes"
$Metric = "Process running status and resource usage"
$Unit = "boolean"

# List of processes to monitor
[string[]]$TargetProcesses = @(
    'OfficeClickToRun',
    'ollama',
    'ollama app',
    'OneApp.IGCC.WinService',
    'OpenConsole',
    'PhoneExperienceHost',
    'Postman',
    'powershell',
    'Registry',
    'RemoteDesktopManager',
    'RstMwService',
    'RtkAudUService64',
    'RuntimeBroker',
    'SearchHost',
    'spoolsv',
    'wslservice'
)

# === HELPER: Get process status safely ===
function Get-ProcessStatus {
    param([string]$Name)
    try {
        $procs = Get-Process -Name $Name -ErrorAction Stop
        if (-not $procs -or $procs.Count -eq 0) {
            return @{
                Success = $false
                Error   = "No process found"
                Output  = @{
                    Name       = $Name
                    Id         = $null
                    CPUSeconds = $null
                    WorkingSet = $null
                }
            }
        }

        # Take the first instance
        $p = $procs | Select-Object -First 1

        return @{
            Success = $true
            Error   = $null
            Output  = @{
                Name       = $p.ProcessName
                Id         = $p.Id
                CPUSeconds = [Math]::Round($p.CPU, 2)
                WorkingSet = $p.WorkingSet64
            }
        }
    }
    catch {
        return @{
            Success = $false
            Error   = "Process not found"
            Output  = @{
                Name       = $Name
                Id         = $null
                CPUSeconds = $null
                WorkingSet = $null
            }
        }
    }
}

# === MAIN: Run each process as a separate monitor ===
$AllResults = @()
$Hostname = $env:COMPUTERNAME

foreach ($procName in $TargetProcesses) {
    $safeName = $procName -replace '[^\w\-]', '_'  # sanitize name
    $checkName = "MVTSVR $CheckBaseName $safeName"
    Write-Host ("Checking process: " + $procName) -ForegroundColor Cyan

    $result = Get-ProcessStatus -Name $procName

    $check = @{
        Name      = $checkName
        Type      = $Type
        Category  = $Category
        Metric    = $Metric
        Unit      = $Unit
        Success   = $result.Success
        Timestamp = (Get-Date).ToString("s")   # sortable ISO8601
        Error     = $result.Error
        Output    = $result.Output
        CsvPath   = $null
    }

    $AllResults += $check
}

# === JSON SERIALIZATION (no ConvertTo-Json in PSv2) ===
Add-Type -AssemblyName System.Web.Extensions
$JavaScriptSerializer = New-Object System.Web.Script.Serialization.JavaScriptSerializer
$JsonPayload = $JavaScriptSerializer.Serialize($AllResults)

# Save to file
$OutputFile = "MonitoringResults.json"
Set-Content -Path $OutputFile -Value $JsonPayload -Encoding UTF8
Write-Output "✅ Process checks completed. Results saved to $OutputFile"

# === OPTIONAL: POST TO API (PSv2 compatible) ===
# $ApiEndpoint = "http://localhost:8888/Results"
# $ApiKey = "V7xP5wR8tY6mN1oA4hB7cE0dF9gH2jK5nM8pQ3rT6vW7yZ1"
# if ($ApiEndpoint) {
#     try {
#         $wc = New-Object System.Net.WebClient
#         $wc.Headers.Add("Content-Type", "application/json")
#         $wc.Headers.Add("X-API-Key", $ApiKey)
#         $response = $wc.UploadString($ApiEndpoint, "POST", $JsonPayload)
#         Write-Output "✅ Successfully posted results to $ApiEndpoint"
#     }
#     catch {
#         Write-Error ("❌ Failed to send data to " + $ApiEndpoint + ". Error: " + $_.Exception.Message)
#     }
# }
