# === CONFIGURATION ===
$CheckBaseName = "Process"
$Type = "Process"
$Category = "Processes"
$Metric = "Process running status and resource usage"
$Unit = "boolean"

# List of processes to monitor (PowerShell 2.0 compatible)
$TargetProcesses = 'OfficeClickToRun', 'ollama', 'ollama app', 'OneApp.IGCC.WinService', 'OpenConsole', 'PhoneExperienceHost', 'Postman', 'powershell', 'Registry', 'RemoteDesktopManager', 'RstMwService', 'RtkAudUService64', 'RuntimeBroker', 'SearchHost', 'spoolsv', 'wslservice'

# === HELPER: Get process status safely (PowerShell 2.0 compatible) ===
function Get-ProcessStatus {
    param([string]$Name)
    try {
        # Use WMI instead of Get-Process for PowerShell 2.0 compatibility
        $wmiQuery = "SELECT * FROM Win32_Process WHERE Name LIKE '%$Name%'"
        $procs = Get-WmiObject -Query $wmiQuery

        if (-not $procs) {
            $outputObj = New-Object PSObject
            $outputObj | Add-Member -MemberType NoteProperty -Name "Name" -Value $Name
            $outputObj | Add-Member -MemberType NoteProperty -Name "Id" -Value $null
            $outputObj | Add-Member -MemberType NoteProperty -Name "CPUSeconds" -Value $null
            $outputObj | Add-Member -MemberType NoteProperty -Name "WorkingSet" -Value $null

            $resultObj = New-Object PSObject
            $resultObj | Add-Member -MemberType NoteProperty -Name "Success" -Value $false
            $resultObj | Add-Member -MemberType NoteProperty -Name "Error" -Value "No process found"
            $resultObj | Add-Member -MemberType NoteProperty -Name "Output" -Value $outputObj
            return $resultObj
        }

        # Take the first instance
        $p = $procs | Select-Object -First 1

        $outputObj = New-Object PSObject
        $outputObj | Add-Member -MemberType NoteProperty -Name "Name" -Value $p.Name
        $outputObj | Add-Member -MemberType NoteProperty -Name "Id" -Value $p.ProcessId
        $outputObj | Add-Member -MemberType NoteProperty -Name "CPUSeconds" -Value $null  # CPU time not easily available via WMI
        $outputObj | Add-Member -MemberType NoteProperty -Name "WorkingSet" -Value $p.WorkingSetSize

        $resultObj = New-Object PSObject
        $resultObj | Add-Member -MemberType NoteProperty -Name "Success" -Value $true
        $resultObj | Add-Member -MemberType NoteProperty -Name "Error" -Value $null
        $resultObj | Add-Member -MemberType NoteProperty -Name "Output" -Value $outputObj
        return $resultObj
    }
    catch {
        $outputObj = New-Object PSObject
        $outputObj | Add-Member -MemberType NoteProperty -Name "Name" -Value $Name
        $outputObj | Add-Member -MemberType NoteProperty -Name "Id" -Value $null
        $outputObj | Add-Member -MemberType NoteProperty -Name "CPUSeconds" -Value $null
        $outputObj | Add-Member -MemberType NoteProperty -Name "WorkingSet" -Value $null

        $resultObj = New-Object PSObject
        $resultObj | Add-Member -MemberType NoteProperty -Name "Success" -Value $false
        $resultObj | Add-Member -MemberType NoteProperty -Name "Error" -Value "Process not found"
        $resultObj | Add-Member -MemberType NoteProperty -Name "Output" -Value $outputObj
        return $resultObj
    }
}

# === MAIN: Run each process as a separate monitor ===
$AllResults = New-Object System.Collections.ArrayList

foreach ($procName in $TargetProcesses) {
    $safeName = $procName -replace '[^\w\-]', '_'  # sanitize name
    $checkName = "MVTSVR $CheckBaseName $safeName"
    Write-Host ("Checking process: " + $procName) -ForegroundColor Cyan

    $result = Get-ProcessStatus -Name $procName

    # Create check object using PSObject (PowerShell 2.0 compatible)
    $check = New-Object PSObject
    $check | Add-Member -MemberType NoteProperty -Name "Name" -Value $checkName
    $check | Add-Member -MemberType NoteProperty -Name "Type" -Value $Type
    $check | Add-Member -MemberType NoteProperty -Name "Category" -Value $Category
    $check | Add-Member -MemberType NoteProperty -Name "Metric" -Value $Metric
    $check | Add-Member -MemberType NoteProperty -Name "Unit" -Value $Unit
    $check | Add-Member -MemberType NoteProperty -Name "Success" -Value $result.Success
    $check | Add-Member -MemberType NoteProperty -Name "Timestamp" -Value ((Get-Date).ToString("s"))
    $check | Add-Member -MemberType NoteProperty -Name "Error" -Value $result.Error
    $check | Add-Member -MemberType NoteProperty -Name "Output" -Value $result.Output
    $check | Add-Member -MemberType NoteProperty -Name "CsvPath" -Value $null

    $AllResults.Add($check) | Out-Null
}

# === JSON SERIALIZATION (no ConvertTo-Json in PSv2) ===
Add-Type -AssemblyName System.Web.Extensions
$JavaScriptSerializer = New-Object System.Web.Script.Serialization.JavaScriptSerializer
$JsonPayload = $JavaScriptSerializer.Serialize($AllResults)

# Save to file
$OutputFile = "MonitoringResults.json"
Set-Content -Path $OutputFile -Value $JsonPayload -Encoding UTF8
Write-Output "✅ Process checks completed. Results saved to $OutputFile"

# === OPTIONAL: POST TO API (PSv2 compatible) ===
# $ApiEndpoint = "http://localhost:8888/Results"
# $ApiKey = "V7xP5wR8tY6mN1oA4hB7cE0dF9gH2jK5nM8pQ3rT6vW7yZ1"
# if ($ApiEndpoint) {
#     try {
#         $wc = New-Object System.Net.WebClient
#         $wc.Headers.Add("Content-Type", "application/json")
#         $wc.Headers.Add("X-API-Key", $ApiKey)
#         $response = $wc.UploadString($ApiEndpoint, "POST", $JsonPayload)
#         Write-Output "✅ Successfully posted results to $ApiEndpoint"
#     }
#     catch {
#         Write-Error ("❌ Failed to send data to " + $ApiEndpoint + ". Error: " + $_.Exception.Message)
#     }
# }
